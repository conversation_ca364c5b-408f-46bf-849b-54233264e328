{"private": true, "type": "module", "scripts": {"build": "vite build", "clean": "rm -rf public/vendor/tabler/* && rm -rf public/vendor/jquery/* && rm -rf public/vendor/bootstrap/* && rm -rf public/vendor/bootstrap-icons/*", "clean:tabler": "rm -rf public/vendor/tabler/*", "clean:jquery": "rm -rf public/vendor/jquery/*", "clean:bootstrap": "rm -rf public/vendor/bootstrap/*", "clean:bootstrap-icons": "rm -rf public/vendor/bootstrap-icons/*", "build:clean": "npm run clean && npm run build", "verify:all": "echo '=== VERIFYING ALL VENDOR ASSETS ===' && echo 'Tabler Core:' && find public/vendor/tabler -type f | wc -l && echo 'jQuery:' && find public/vendor/jquery -type f | wc -l && echo 'Bootstrap:' && find public/vendor/bootstrap -type f | wc -l && echo 'Bootstrap Icons:' && find public/vendor/bootstrap-icons -type f | wc -l && echo 'Total:' && find public/vendor/tabler public/vendor/jquery public/vendor/bootstrap public/vendor/bootstrap-icons -type f | wc -l"}, "devDependencies": {"vite": "^7.0.6"}, "dependencies": {"@tabler/core": "^1.4.0", "bootstrap": "^5.3.7", "bootstrap-icons": "^1.13.1", "jquery": "^3.7.1"}}